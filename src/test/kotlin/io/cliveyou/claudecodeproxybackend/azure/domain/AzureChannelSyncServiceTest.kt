package io.cliveyou.claudecodeproxybackend.azure.domain

import com.fasterxml.jackson.databind.ObjectMapper
import io.cliveyou.claudecodeproxybackend.azure.infrastructure.entity.AzureChannel
import io.cliveyou.claudecodeproxybackend.azure.infrastructure.repository.AzureChannelRepository
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.KeyChannel
import io.cliveyou.claudecodeproxybackend.keymanagment.domain.TokenManagementService
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.ChannelSyncStatus
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonKeyStatus
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.entity.CommonTokenKey
import io.cliveyou.claudecodeproxybackend.keymanagment.infrastructure.repository.ChannelSyncStatusRepository
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@ExtendWith(MockitoExtension::class)
class AzureChannelSyncServiceTest {

    @Mock
    private lateinit var azureChannelRepository: AzureChannelRepository

    @Mock
    private lateinit var channelSyncStatusRepository: ChannelSyncStatusRepository

    @Mock
    private lateinit var tokenManagementService: TokenManagementService

    private lateinit var objectMapper: ObjectMapper
    private lateinit var azureChannelSyncService: AzureChannelSyncService

    @BeforeEach
    fun setUp() {
        objectMapper = ObjectMapper()
        azureChannelSyncService = AzureChannelSyncService(
            azureChannelRepository,
            channelSyncStatusRepository,
            tokenManagementService,
            objectMapper
        )
    }

    @Test
    fun `should perform incremental sync successfully when new channels exist`() = runBlocking {
        // Given
        val lastSyncStatus = ChannelSyncStatus(
            id = 1L,
            syncType = "azure_openai",
            lastSyncId = 0L,
            lastSyncTime = LocalDateTime.now(),
            syncStatus = "SUCCESS"
        )

        val azureChannel = AzureChannel(
            id = 1L,
            key = "sk-test-key-123",
            baseUrl = "https://api.openai.com/v1",
            status = 1L,
            name = "Test Azure Channel",
            balance = 100.0
        )

        val savedCommonTokenKey = CommonTokenKey(
            id = 1L,
            accessToken = "sk-test-key-123",
            domain = "https://api.openai.com/v1",
            type = KeyChannel.Openai,
            status = CommonKeyStatus.ACTIVE,
            name = "Test Azure Channel"
        )

        // When
        whenever(channelSyncStatusRepository.findBySyncType("azure_openai"))
            .thenReturn(Optional.of(lastSyncStatus))
        whenever(azureChannelRepository.findChannelsForSync(0L))
            .thenReturn(listOf(azureChannel))
        whenever(azureChannelRepository.findByBaseUrlAndKey(any(), any()))
            .thenReturn(listOf(azureChannel))
        whenever(tokenManagementService.createTokenKey(any<CommonTokenKey>()))
            .thenReturn(savedCommonTokenKey)

        // Then
        val result = azureChannelSyncService.performIncrementalSync()

        assertTrue(result.success)
        assertEquals(1, result.syncCount)
        assertEquals("Successfully synced 1 channels", result.message)

        verify(tokenManagementService).createTokenKey(any<CommonTokenKey>())
        verify(channelSyncStatusRepository, times(2)).save(any())
    }

    @Test
    fun `should skip sync when no new channels exist`() = runBlocking {
        // Given
        val lastSyncStatus = ChannelSyncStatus(
            id = 1L,
            syncType = "azure_openai",
            lastSyncId = 10L,
            lastSyncTime = LocalDateTime.now(),
            syncStatus = "SUCCESS"
        )

        // When
        whenever(channelSyncStatusRepository.findBySyncType("azure_openai"))
            .thenReturn(Optional.of(lastSyncStatus))
        whenever(azureChannelRepository.findChannelsForSync(10L))
            .thenReturn(emptyList())

        // Then
        val result = azureChannelSyncService.performIncrementalSync()

        assertTrue(result.success)
        assertEquals(0, result.syncCount)
        assertEquals("No new channels to sync", result.message)

        verify(tokenManagementService, never()).createTokenKey(any<CommonTokenKey>())
    }

    @Test
    fun `should skip channel with empty key`() = runBlocking {
        // Given
        val lastSyncStatus = ChannelSyncStatus(
            id = 1L,
            syncType = "azure_openai",
            lastSyncId = 0L,
            lastSyncTime = LocalDateTime.now(),
            syncStatus = "SUCCESS"
        )

        val azureChannelWithEmptyKey = AzureChannel(
            id = 1L,
            key = "",
            baseUrl = "https://api.openai.com/v1",
            status = 1L,
            name = "Test Channel with Empty Key"
        )

        // When
        whenever(channelSyncStatusRepository.findBySyncType("azure_openai"))
            .thenReturn(Optional.of(lastSyncStatus))
        whenever(azureChannelRepository.findChannelsForSync(0L))
            .thenReturn(listOf(azureChannelWithEmptyKey))

        // Then
        val result = azureChannelSyncService.performIncrementalSync()

        assertTrue(result.success)
        assertEquals(0, result.syncCount)
        assertEquals("Successfully synced 0 channels", result.message)

        verify(tokenManagementService, never()).createTokenKey(any<CommonTokenKey>())
    }

    @Test
    fun `should handle sync failure gracefully`() = runBlocking {
        // Given
        val lastSyncStatus = ChannelSyncStatus(
            id = 1L,
            syncType = "azure_openai",
            lastSyncId = 0L,
            lastSyncTime = LocalDateTime.now(),
            syncStatus = "SUCCESS"
        )

        // When
        whenever(channelSyncStatusRepository.findBySyncType("azure_openai"))
            .thenReturn(Optional.of(lastSyncStatus))
        whenever(azureChannelRepository.findChannelsForSync(0L))
            .thenThrow(RuntimeException("Database connection failed"))

        // Then
        val result = azureChannelSyncService.performIncrementalSync()

        assertEquals(false, result.success)
        assertEquals(0, result.syncCount)
        assertTrue(result.message.contains("Database connection failed"))

        verify(channelSyncStatusRepository).save(argThat { 
            syncStatus == "FAILED" && errorMessage?.contains("Database connection failed") == true 
        })
    }

    @Test
    fun `should create sync status when none exists`() = runBlocking {
        // Given
        val azureChannel = AzureChannel(
            id = 1L,
            key = "sk-test-key-123",
            baseUrl = "https://api.openai.com/v1",
            status = 1L,
            name = "Test Azure Channel"
        )

        val savedCommonTokenKey = CommonTokenKey(
            id = 1L,
            accessToken = "sk-test-key-123",
            domain = "https://api.openai.com/v1",
            type = KeyChannel.Openai,
            status = CommonKeyStatus.ACTIVE,
            name = "Test Azure Channel"
        )

        // When
        whenever(channelSyncStatusRepository.findBySyncType("azure_openai"))
            .thenReturn(Optional.empty())
        whenever(azureChannelRepository.findChannelsForSync(0L))
            .thenReturn(listOf(azureChannel))
        whenever(azureChannelRepository.findByBaseUrlAndKey(any(), any()))
            .thenReturn(listOf(azureChannel))
        whenever(tokenManagementService.createTokenKey(any<CommonTokenKey>()))
            .thenReturn(savedCommonTokenKey)

        // Then
        val result = azureChannelSyncService.performIncrementalSync()

        assertTrue(result.success)
        assertEquals(1, result.syncCount)

        verify(channelSyncStatusRepository, times(2)).save(any())
    }
}
