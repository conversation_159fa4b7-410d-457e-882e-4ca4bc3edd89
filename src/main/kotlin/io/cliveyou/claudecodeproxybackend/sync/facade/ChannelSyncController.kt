package io.cliveyou.claudecodeproxybackend.sync.facade

import io.cliveyou.claudecodeproxybackend.common.response.PlatformResult
import io.cliveyou.claudecodeproxybackend.sync.application.service.ChannelSyncTestService
import io.cliveyou.claudecodeproxybackend.sync.application.service.DataSourceTestService
import io.cliveyou.claudecodeproxybackend.sync.application.timer.ChannelSyncTimer
import io.cliveyou.claudecodeproxybackend.sync.domain.ChannelSyncService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.bind.annotation.*

/**
 * Channel同步控制器
 * 提供手动触发同步的API接口
 */
@RestController
@RequestMapping("/api/v1/sync")
class ChannelSyncController(
    private val channelSyncTimer: ChannelSyncTimer,
    private val channelSyncTestService: ChannelSyncTestService
) {
    
    private val log = KotlinLogging.logger {}

    /**
     * 手动触发Google Vertex AI密钥同步
     */
    @PostMapping("/google-vertex-ai/trigger")
    fun triggerGoogleVertexAISync(): PlatformResult<ChannelSyncService.SyncResult> {
        return try {
            log.info { "Channel Sync API | Manual sync triggered via API" }
            
            val result = channelSyncTimer.triggerManualSync()
            
            if (result.success) {
                PlatformResult.success(result)
            } else {
                PlatformResult.error("SYNC_FAILED", result.message)
            }
            
        } catch (e: Exception) {
            log.error(e) { "Channel Sync API | Manual sync failed via API | Failed: ${e.message}" }
            PlatformResult.error("SYNC_ERROR", "同步过程中发生错误: ${e.message}")
        }
    }

    /**
     * 获取同步状态信息
     */
    @GetMapping("/google-vertex-ai/status")
    fun getSyncStatus(): PlatformResult<Map<String, Any>> {
        return try {
            // 这里可以添加获取同步状态的逻辑
            val status = mapOf(
                "syncType" to "google_vertex_ai",
                "message" to "Use POST /api/v1/sync/google-vertex-ai/trigger to trigger manual sync"
            )

            PlatformResult.success(status)

        } catch (e: Exception) {
            log.error(e) { "Channel Sync API | Failed to get sync status | Failed: ${e.message}" }
            PlatformResult.error("STATUS_ERROR", "获取状态失败: ${e.message}")
        }
    }

    /**
     * 测试数据源连接
     */
    @GetMapping("/test-connections")
    fun testConnections(): PlatformResult<Map<String, Any>> {
        return try {
            log.info { "Channel Sync API | Testing data source connections" }

            val result = channelSyncTestService.testDataSourceConnections()

            PlatformResult.success(result)

        } catch (e: Exception) {
            log.error(e) { "Channel Sync API | Failed to test connections | Failed: ${e.message}" }
            PlatformResult.error("TEST_ERROR", "测试连接失败: ${e.message}")
        }
    }
}
