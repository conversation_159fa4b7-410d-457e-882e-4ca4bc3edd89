package io.cliveyou.claudecodeproxybackend.azure.infrastructure.entity

import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * Azure Channel实体类
 * 对应Azure MySQL数据库中的channels表
 */
@Entity
@Table(name = "channels")
data class AzureChannel(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long? = null,

    /**
     * 类型
     */
    @Column(name = "type")
    val type: Long? = 0,

    /**
     * 密钥内容 - 存储API Key
     */
    @Column(name = "`key`", columnDefinition = "LONGTEXT")
    val key: String,

    /**
     * 基础URL
     */
    @Column(name = "base_url", columnDefinition = "LONGTEXT")
    val baseUrl: String? = null,

    /**
     * OpenAI组织
     */
    @Column(name = "open_ai_organization", columnDefinition = "LONGTEXT")
    val openAiOrganization: String? = null,

    /**
     * 测试模型
     */
    @Column(name = "test_model", columnDefinition = "LONGTEXT")
    val testModel: String? = null,

    /**
     * 状态 (1=启用, 0=禁用)
     */
    @Column(name = "status")
    val status: Long? = 1,

    /**
     * 名称
     */
    @Column(name = "name", length = 191)
    val name: String? = null,

    /**
     * 模型列表 JSON
     */
    @Column(name = "models", columnDefinition = "LONGTEXT")
    val models: String? = null,

    /**
     * 余额
     */
    @Column(name = "balance")
    val balance: Double? = null,

    /**
     * 已使用配额
     */
    @Column(name = "used_quota")
    val usedQuota: Long? = 0,

    /**
     * 模型映射
     */
    @Column(name = "model_mapping", columnDefinition = "TEXT")
    val modelMapping: String? = null,

    /**
     * 状态码映射
     */
    @Column(name = "status_code_mapping", length = 1024)
    val statusCodeMapping: String? = "",

    /**
     * 优先级
     */
    @Column(name = "priority")
    val priority: Long? = 0,

    /**
     * 自动禁用
     */
    @Column(name = "auto_ban")
    val autoBan: Long? = 1,

    /**
     * 其他信息
     */
    @Column(name = "other_info", columnDefinition = "LONGTEXT")
    val otherInfo: String? = null,

    /**
     * 设置
     */
    @Column(name = "settings", columnDefinition = "LONGTEXT")
    val settings: String? = null,

    /**
     * 分组
     */
    @Column(name = "`group`", length = 64)
    val group: String? = "default",

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    val createdTime: LocalDateTime? = null,

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    val updatedTime: LocalDateTime? = null
)
